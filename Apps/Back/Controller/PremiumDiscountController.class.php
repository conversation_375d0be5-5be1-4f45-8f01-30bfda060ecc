<?php
namespace Back\Controller;
use Think\Controller;

class PremiumDiscountController extends CommController {
    
    function _initialize(){
        parent::_initialize();

        // 定义操作符选项
        $this->operators = array(
            '>' => '大于',
            '<' => '小于',
            '=' => '等于',
            '>=' => '大于等于',
            '<=' => '小于等于',
            '!=' => '不等于',
            'between' => '介于',
            'in' => '包含于'
        );

        // 定义条件类型
        $this->condition_types = array(
            'single' => '单值条件',
            'range' => '范围条件'
        );
    }

    /**
     * 升贴水规则列表
     */
    public function index(){
        $page = I('get.page', 1);
        $pageSize = 20;
        
        $model = M('PremiumDiscountRule');
        $count = $model->count();
        
        $rules = $model->order('id desc')
                      ->page($page, $pageSize)
                      ->select();
        
        // 获取每个规则的详情数量
        foreach($rules as &$rule) {
            $rule['detail_count'] = M('PremiumDiscountRuleDetail')
                                  ->where(array('rule_id' => $rule['id']))
                                  ->count();
        }
        
        $this->assign('rules', $rules);
        $this->assign('count', $count);
        $this->assign('page', $page);
        $this->assign('pageSize', $pageSize);
        $this->display();
    }

    /**
     * 添加规则页面
     */
    public function add(){
        $this->display();
    }

    /**
     * 执行添加规则
     */
    public function runAdd(){
        $model = M('PremiumDiscountRule');
        
        $data = array(
            'rule_name' => I('post.rule_name'),
            'is_enabled' => I('post.is_enabled', 1),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        // 验证规则名称是否重复
        $exists = $model->where(array('rule_name' => $data['rule_name']))->find();
        if($exists) {
            die(json_encode(array('code' => 0, 'msg' => '规则名称已存在')));
        }
        
        if($model->add($data)) {
            die(json_encode(array('code' => 1, 'msg' => '添加成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '添加失败')));
        }
    }

    /**
     * 编辑规则页面
     */
    public function edit(){
        $id = I('get.id');
        $rule = M('PremiumDiscountRule')->find($id);
        
        if(!$rule) {
            $this->error('规则不存在');
        }
        
        $this->assign('rule', $rule);
        $this->display();
    }

    /**
     * 执行编辑规则
     */
    public function runEdit(){
        $model = M('PremiumDiscountRule');
        $id = I('post.id');
        
        $data = array(
            'id' => $id,
            'rule_name' => I('post.rule_name'),
            'is_enabled' => I('post.is_enabled', 1),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        // 验证规则名称是否重复（排除当前记录）
        $exists = $model->where(array('rule_name' => $data['rule_name'], 'id' => array('neq', $id)))->find();
        if($exists) {
            die(json_encode(array('code' => 0, 'msg' => '规则名称已存在')));
        }
        
        if($model->save($data)) {
            die(json_encode(array('code' => 1, 'msg' => '修改成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '修改失败')));
        }
    }

    /**
     * 删除规则
     */
    public function del(){
        $id = I('get.id');
        
        // 检查是否有关联的规则详情
        $detailCount = M('PremiumDiscountRuleDetail')->where(array('rule_id' => $id))->count();
        if($detailCount > 0) {
            die(json_encode(array('code' => 0, 'msg' => '请先删除该规则下的所有详情配置')));
        }
        
        if(M('PremiumDiscountRule')->delete($id)) {
            die(json_encode(array('code' => 1, 'msg' => '删除成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '删除失败')));
        }
    }

    /**
     * 切换规则启用状态
     */
    public function toggleStatus(){
        $id = I('get.id');
        $rule = M('PremiumDiscountRule')->find($id);
        
        if(!$rule) {
            die(json_encode(array('code' => 0, 'msg' => '规则不存在')));
        }
        
        $newStatus = $rule['is_enabled'] ? 0 : 1;
        $data = array(
            'id' => $id,
            'is_enabled' => $newStatus,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        if(M('PremiumDiscountRule')->save($data)) {
            $statusText = $newStatus ? '启用' : '禁用';
            die(json_encode(array('code' => 1, 'msg' => $statusText . '成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '操作失败')));
        }
    }

    /**
     * 规则详情管理
     */
    public function detail(){
        $ruleId = I('get.rule_id');
        $rule = M('PremiumDiscountRule')->find($ruleId);

        if(!$rule) {
            $this->error('规则不存在');
        }

        // 获取规则详情，关联条件字段信息
        $details = M('PremiumDiscountRuleDetail')
                  ->alias('d')
                  ->join('LEFT JOIN sd_premium_discount_condition_field f ON d.field_id = f.id')
                  ->field('d.*, f.field_name, f.field_code, f.field_type, f.field_unit, f.is_range, f.select_options')
                  ->where(array('d.rule_id' => $ruleId))
                  ->order('d.id desc')
                  ->select();

        // 处理显示数据
        foreach($details as &$detail) {
            // 处理条件值显示
            if($detail['condition_type'] == 'range') {
                $detail['condition_display'] = $detail['min_value'] . ' ~ ' . $detail['max_value'];
                if($detail['field_unit']) {
                    $detail['condition_display'] .= ' ' . $detail['field_unit'];
                }
            } else {
                $detail['condition_display'] = $detail['condition_value'];
                if($detail['field_unit']) {
                    $detail['condition_display'] .= ' ' . $detail['field_unit'];
                }
            }
        }

        $this->assign('rule', $rule);
        $this->assign('details', $details);
        $this->assign('operators', $this->operators);
        $this->assign('condition_types', $this->condition_types);
        $this->display();
    }

    /**
     * 添加规则详情
     */
    public function addDetail(){
        $ruleId = I('get.rule_id');
        $rule = M('PremiumDiscountRule')->find($ruleId);

        if(!$rule) {
            $this->error('规则不存在');
        }

        // 获取启用的条件字段
        $condition_fields = M('PremiumDiscountConditionField')
                           ->where(array('is_enabled' => 1))
                           ->order('sort_order asc, id asc')
                           ->select();

        $this->assign('rule', $rule);
        $this->assign('condition_fields', $condition_fields);
        $this->assign('operators', $this->operators);
        $this->assign('condition_types', $this->condition_types);
        $this->display();
    }

    /**
     * 执行添加规则详情
     */
    public function runAddDetail(){
        $model = M('PremiumDiscountRuleDetail');

        $data = array(
            'rule_id' => I('post.rule_id'),
            'field_id' => I('post.field_id'),
            'condition_type' => I('post.condition_type'),
            'operator' => I('post.operator'),
            'discount_value' => I('post.discount_value'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        );

        // 根据条件类型设置条件值
        if($data['condition_type'] == 'range') {
            $data['min_value'] = I('post.min_value');
            $data['max_value'] = I('post.max_value');
            $data['condition_value'] = null;
        } else {
            $data['condition_value'] = I('post.condition_value');
            $data['min_value'] = null;
            $data['max_value'] = null;
        }

        if($model->add($data)) {
            die(json_encode(array('code' => 1, 'msg' => '添加成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '添加失败')));
        }
    }

    /**
     * 编辑规则详情
     */
    public function editDetail(){
        $id = I('get.id');
        $detail = M('PremiumDiscountRuleDetail')
                 ->alias('d')
                 ->join('LEFT JOIN sd_premium_discount_condition_field f ON d.field_id = f.id')
                 ->field('d.*, f.field_name, f.field_code, f.field_type, f.field_unit, f.is_range, f.select_options')
                 ->where(array('d.id' => $id))
                 ->find();

        if(!$detail) {
            $this->error('详情不存在');
        }

        $rule = M('PremiumDiscountRule')->find($detail['rule_id']);

        // 获取启用的条件字段
        $condition_fields = M('PremiumDiscountConditionField')
                           ->where(array('is_enabled' => 1))
                           ->order('sort_order asc, id asc')
                           ->select();

        $this->assign('detail', $detail);
        $this->assign('rule', $rule);
        $this->assign('condition_fields', $condition_fields);
        $this->assign('operators', $this->operators);
        $this->assign('condition_types', $this->condition_types);
        $this->display();
    }

    /**
     * 执行编辑规则详情
     */
    public function runEditDetail(){
        $model = M('PremiumDiscountRuleDetail');

        $data = array(
            'id' => I('post.id'),
            'field_id' => I('post.field_id'),
            'condition_type' => I('post.condition_type'),
            'operator' => I('post.operator'),
            'discount_value' => I('post.discount_value'),
            'updated_at' => date('Y-m-d H:i:s')
        );

        // 根据条件类型设置条件值
        if($data['condition_type'] == 'range') {
            $data['min_value'] = I('post.min_value');
            $data['max_value'] = I('post.max_value');
            $data['condition_value'] = null;
        } else {
            $data['condition_value'] = I('post.condition_value');
            $data['min_value'] = null;
            $data['max_value'] = null;
        }

        if($model->save($data)) {
            die(json_encode(array('code' => 1, 'msg' => '修改成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '修改失败')));
        }
    }

    /**
     * 删除规则详情
     */
    public function delDetail(){
        $id = I('get.id');
        
        if(M('PremiumDiscountRuleDetail')->delete($id)) {
            die(json_encode(array('code' => 1, 'msg' => '删除成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '删除失败')));
        }
    }
}
